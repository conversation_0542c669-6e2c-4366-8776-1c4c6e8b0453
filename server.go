package main

import (
	"context"
	"errors"
	"log/slog"
	"sync"

	"github.com/BhaumikTalwar/Amrita/models/dto"
	gameapi "github.com/BhaumikTalwar/Amrita/service/GameApi"
)

type PlayerState int

const (
	PlayerStateActive PlayerState = iota
	PlayerStateLeft
)

type PlayerInfo struct {
	Username     string
	SessionToken string
	Symbol       string
	State        PlayerState
}

type ChessMove struct {
	From      string `json:"from"`
	To        string `json:"to"`
	Promotion string `json:"promotion,omitempty"`
	San       string `json:"san"`
	Fen       string `json:"fen"`
}

type Chess struct{}

func NewGame() gameapi.Game {
	return &Chess{}
}

type ChessInstance struct {
	roomID string
	config gameapi.GameConfig
	mu     sync.Mutex

	playerInfo   map[string]*PlayerInfo
	currentTurn  string
	gameStarted  bool
	isGameOver   bool
	winner       string
	moves        []ChessMove
	currentFen   string
	gameResult   string
}

func (g *Chess) NewInstance(config gameapi.GameConfig, roomID string) gameapi.GameInstance {
	if err := g.ValidateConfig(config); err != nil {
		slog.Error("Error Cant initialize the Instance Config not valid", slog.Any("Error", err))
		return nil
	}
	return &ChessInstance{
		roomID:      roomID,
		config:      config,
		playerInfo:  make(map[string]*PlayerInfo),
		isGameOver:  false,
		moves:       make([]ChessMove, 0),
		currentFen:  "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1", // Starting position
		currentTurn: "Player1", // White starts first
		gameStarted: false,
	}
}

func (g *Chess) ValidateConfig(config gameapi.GameConfig) error {
	return nil
}

func (g *ChessInstance) HandlePlayerJoin(ctx context.Context, playerID string, sessionId string, playerData interface{}) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if len(g.playerInfo) >= 2 {
		return errors.New("game is full")
	}

	pData, ok := playerData.(map[string]interface{})
	if !ok {
		return errors.New("invalid player data format")
	}
	name, ok := pData["username"].(string)
	if !ok {
		return errors.New("username not provided")
	}

	symbol := "Player1"
	if len(g.playerInfo) == 1 {
		symbol = "Player2"
	}

	g.playerInfo[playerID] = &PlayerInfo{
		Username:     name,
		SessionToken: sessionId,
		Symbol:       symbol,
		State:        PlayerStateActive,
	}

	slog.Info("Player joined chess game", slog.String("playerID", playerID), slog.String("symbol", symbol))
	return nil
}

func (g *ChessInstance) HandlePlayerLeave(ctx context.Context, playerID string) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if player, exists := g.playerInfo[playerID]; exists {
		player.State = PlayerStateLeft
		slog.Info("Player left chess game", slog.String("playerID", playerID))
	}
	return nil
}

func (g *ChessInstance) HandleGameAction(ctx context.Context, playerID string, action interface{}) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if g.isGameOver {
		return errors.New("game is already over")
	}

	player, exists := g.playerInfo[playerID]
	if !exists || player.State != PlayerStateActive {
		return errors.New("player not found or inactive")
	}

	if player.Symbol != g.currentTurn {
		return errors.New("not your turn")
	}

	actionMap, ok := action.(map[string]interface{})
	if !ok {
		return errors.New("invalid action format")
	}

	// Handle chess move
	if moveData, exists := actionMap["move"]; exists {
		moveMap, ok := moveData.(map[string]interface{})
		if !ok {
			return errors.New("invalid move format")
		}

		move := ChessMove{
			From: getString(moveMap, "from"),
			To:   getString(moveMap, "to"),
			San:  getString(moveMap, "san"),
			Fen:  getString(moveMap, "fen"),
		}

		if promotion, exists := moveMap["promotion"]; exists {
			if promStr, ok := promotion.(string); ok {
				move.Promotion = promStr
			}
		}

		g.moves = append(g.moves, move)
		g.currentFen = move.Fen

		// Switch turns
		if g.currentTurn == "Player1" {
			g.currentTurn = "Player2"
		} else {
			g.currentTurn = "Player1"
		}

		slog.Info("Chess move made", slog.String("playerID", playerID), slog.Any("move", move))
	}

	// Handle timeout
	if _, exists := actionMap["timeout"]; exists {
		g.isGameOver = true
		// The player who timed out loses
		if player.Symbol == "Player1" {
			g.winner = "Player2"
		} else {
			g.winner = "Player1"
		}
		g.gameResult = "timeout"
		slog.Info("Player timed out", slog.String("playerID", playerID))
	}

	return nil
}

func getString(m map[string]interface{}, key string) string {
	if val, exists := m[key]; exists {
		if str, ok := val.(string); ok {
			return str
		}
	}
	return ""
}

func (g *ChessInstance) GetGameState(ctx context.Context, playerID string) (interface{}, error) {
	g.mu.Lock()
	defer g.mu.Unlock()

	return map[string]interface{}{
		"players":     g.buildPlayerNameMap(),
		"currentTurn": g.currentTurn,
		"gameStarted": g.gameStarted,
		"isGameOver":  g.isGameOver,
		"winner":      g.winner,
		"moves":       g.moves,
		"currentFen":  g.currentFen,
		"gameResult":  g.gameResult,
	}, nil
}

func (g *ChessInstance) GetPersonalState(ctx context.Context, playerID string) (interface{}, error) {
	g.mu.Lock()
	defer g.mu.Unlock()

	player, exists := g.playerInfo[playerID]
	if !exists {
		return nil, errors.New("player not found")
	}

	return map[string]interface{}{
		"symbol": player.Symbol,
	}, nil
}

func (g *ChessInstance) StartGame(ctx context.Context) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if len(g.playerInfo) < 2 {
		return errors.New("not enough players")
	}

	g.gameStarted = true
	slog.Info("Chess game started", slog.String("roomID", g.roomID))
	return nil
}

func (g *ChessInstance) EndGame(ctx context.Context, reason dto.GameEndReason) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	g.isGameOver = true
	slog.Info("Chess game ended", slog.String("roomID", g.roomID), slog.Any("reason", reason))
	return nil
}

func (g *ChessInstance) GetRoomID() string {
	return g.roomID
}

func (g *ChessInstance) CalculateFinalResults(lobby_price float64, reason dto.GameEndReason, faultingPlayerID *string) []dto.PlayerResult {
	g.mu.Lock()
	defer g.mu.Unlock()

	// Only calculate results if game is over or insufficient players
	if !g.isGameOver && reason != dto.GameEndReasonInsufficientPlayers {
		return nil
	}

	results := make([]dto.PlayerResult, 0, len(g.playerInfo))

	for playerID, info := range g.playerInfo {
		result := dto.PlayerResult{
			UserID:       playerID,
			UserName:     info.Username,
			SessionToken: info.SessionToken,
			Status:       dto.PlayerStatusUnknown,
			Metadata:     map[string]interface{}{"symbol": info.Symbol},
		}

		// Handle different game end scenarios
		if reason == dto.GameEndReasonInsufficientPlayers {
			// Return lobby price to all players when insufficient players
			result.Rank = 0
			result.CreditInfo = dto.PlayerCreditInfo{Amount: lobby_price, Reason: "Insufficient Players"}
		} else if info.State == PlayerStateLeft {
			// Player who left forfeits
			result.Status = dto.PlayerStatusForfeit
			result.Rank = 2
			result.CreditInfo = dto.PlayerCreditInfo{Amount: 0, Reason: "Forfeited game"}
		} else if faultingPlayerID != nil && *faultingPlayerID == playerID {
			// Player who caused the fault (e.g., timeout, illegal move) loses
			result.Status = dto.PlayerStatusLose
			result.Rank = 2
			result.CreditInfo = dto.PlayerCreditInfo{Amount: 0, Reason: "Game fault"}
		} else if g.winner != "" {
			// Normal game completion with a winner
			if g.winner == info.Symbol {
				result.Status = dto.PlayerStatusWin
				result.Rank = 1
				result.CreditInfo = dto.PlayerCreditInfo{Amount: 2 * lobby_price, Reason: "Winner"}
			} else {
				result.Status = dto.PlayerStatusLose
				result.Rank = 2
				result.CreditInfo = dto.PlayerCreditInfo{Amount: 0, Reason: "Lost"}
			}
		} else if g.isGameOver {
			// Game ended in a draw (stalemate, insufficient material, etc.)
			result.Status = dto.PlayerStatusDraw
			result.Rank = 0
			result.CreditInfo = dto.PlayerCreditInfo{Amount: lobby_price, Reason: "Draw"}
		}

		results = append(results, result)
	}

	return results
}

func (g *ChessInstance) buildPlayerNameMap() map[string]string {
	names := make(map[string]string)
	for _, info := range g.playerInfo {
		if info.State == PlayerStateActive {
			names[info.Symbol] = info.Username
		}
	}
	return names
}
