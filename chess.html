<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, viewport-fit=cover">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#050A24">
    <meta name="format-detection" content="telephone=no">
    <meta name="msapplication-tap-highlight" content="no">
    <title>GamyDay Games - Chess</title>
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>♞</text></svg>">
    <!-- jQuery required for chessboard.js -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Load chess.js library for chess logic -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chess.js/0.10.3/chess.min.js"></script>
    <!-- Load chessboard.js and its CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chessboard-js/1.0.0/chessboard-1.0.0.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chessboard-js/1.0.0/chessboard-1.0.0.min.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Fredoka:wght@400;600;700&family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- Confetti for celebrations -->
    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.9.2/dist/confetti.browser.min.js"></script>
    <!-- Tailwind CSS (for GameDay components only) -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Custom CSS -->
    <link rel="stylesheet" href="chess.css">
    
    <!-- Mobile touch detection script -->
    <script>
        // Detect if device is a touch device
        window.isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        
        // Prevent page scroll when interacting with the board
        document.addEventListener('touchmove', function(e) {
            if (e.target.closest('#game-board') || e.target.closest('#mp-game-board') ||
                e.target.closest('.piece-417db')) {
                e.preventDefault();
            }
        }, {passive: false});
    </script>
    
    <!-- Override styles for chessboard.js drag behavior -->
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            background-color: #0a0a1a;
        }

        /* Only hide overflow during loading screen */
        body.loading {
            overflow: hidden;
        }

        /* Allow scrolling when game is active */
        body.game-active {
            overflow: auto;
        }

        .font-fredoka {
            font-family: 'Fredoka', sans-serif;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-10px);
            }
        }

        .logo-float {
            animation: float 4s ease-in-out infinite;
        }

        @keyframes shine {
            0% {
                background-position: 200% 0;
            }
            100% {
                background-position: -200% 0;
            }
        }

        .progress-bar-shine::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(120deg, rgba(255, 255, 255, 0) 40%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 60%);
            background-size: 200% 100%;
            animation: shine 2s infinite linear;
            z-index: 1;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 1s ease-out forwards;
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
            }
            to {
                opacity: 0;
            }
        }

        .fade-out {
            animation: fadeOut 0.5s ease-out forwards;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.6;
            }
        }

        .text-glow {
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.3), 0 0 20px rgba(255, 255, 255, 0.1);
        }

        .glow-bar {
            box-shadow: 0 0 12px rgba(255, 186, 60, 0.5), 0 0 20px rgba(255, 115, 40, 0.3);
        }

        #loadingScreen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
        }

        #gameUI {
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.5s ease-in, visibility 0.5s;
            min-height: 100vh;
        }

        #gameUI.active {
            visibility: visible;
            opacity: 1;
            min-height: auto;
        }

        /* Ensure body background is correct when game is active */
        body.game-active {
            background-color: #f5f8fa !important;
        }

        /* Ensure dragged pieces are always visible */
        img.piece-417db {
            opacity: 1 !important;
            cursor: pointer;
            touch-action: manipulation;
        }

        /* Fix for ghost pieces during drag */
        body > img[src*="chesspieces"] {
            opacity: 1 !important;
            transform: scale(1.15);
            filter: drop-shadow(3px 6px 8px rgba(0, 0, 0, 0.7));
            pointer-events: none;
            z-index: 9999;
        }

        /* Make squares more touch-friendly */
        .square-55d63 {
            cursor: pointer;
            touch-action: manipulation;
        }

        /* Improve piece visibility on mobile */
        @media (max-width: 768px) {
            img.piece-417db {
                transform: scale(1.05);
                transition: transform 0.1s ease;
            }

            img.piece-417db:active {
                transform: scale(1.1);
            }
        }

        @keyframes modalPop {
            from {
                transform: scale(0.7);
                opacity: 0;
            }
            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        #modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(10, 10, 26, 0.8);
            backdrop-filter: blur(10px);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 2000;
        }

        #modal.show {
            display: flex;
        }

        .modal-content {
            background: linear-gradient(145deg, #1e1b4b, #171336);
            padding: 2.5rem;
            border-radius: 1rem;
            text-align: center;
            border: 1px solid rgba(56, 189, 248, 0.3);
            box-shadow: 0 0 30px rgba(56, 189, 248, 0.2);
            transform: scale(0.7);
            animation: modalPop 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28) forwards;
        }
    </style>
</head>
<body class="bg-[#0a0a1a] loading">
    <!-- GameDay Loading Screen -->
    <div id="loadingScreen"
        class="fade-in-up flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-[#4c1d95] via-[#6b21a8] to-[#1e1b4b] p-6 text-white">
        <div class="mb-8 logo-float">
            <svg width="120" height="120" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="logoGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#38bdf8" />
                        <stop offset="100%" style="stop-color:#6366f1" />
                    </linearGradient>
                    <linearGradient id="logoGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#fbbf24" />
                        <stop offset="100%" style="stop-color:#f97316" />
                    </linearGradient>
                    <linearGradient id="logoGradient3" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#ec4899" />
                        <stop offset="100%" style="stop-color:#d946ef" />
                    </linearGradient>
                </defs>
                <g transform="rotate(15 100 100)">
                    <path
                        d="M100 20 C144.18 20 180 55.82 180 100 C180 144.18 144.18 180 100 180 C55.82 180 20 144.18 20 100"
                        fill="none" stroke="url(#logoGradient1)" stroke-width="20" stroke-linecap="round" />
                    <path d="M100 20 C55.82 20 20 55.82 20 100 C20 144.18 55.82 180 100 180" fill="none"
                        stroke="url(#logoGradient2)" stroke-width="20" stroke-linecap="round"
                        transform="rotate(120 100 100)" />
                    <path d="M20 100 C20 55.82 55.82 20 100 20 C144.18 20 180 55.82 180 100" fill="none"
                        stroke="url(#logoGradient3)" stroke-width="20" stroke-linecap="round"
                        transform="rotate(240 100 100)" />
                    <circle cx="100" cy="100" r="25" fill="white" />
                </g>
            </svg>
        </div>
        <h1 class="font-fredoka text-4xl md:text-5xl font-bold text-center text-white tracking-wider text-glow mb-6">
            GamyDay Games
        </h1>
        <div class="w-full max-w-md mt-4 mb-8">
            <div class="h-4 bg-white/10 rounded-full overflow-hidden shadow-inner relative">
                <div id="progressBar"
                    class="h-full bg-gradient-to-r from-amber-400 to-orange-500 rounded-full transition-all duration-500 ease-out relative progress-bar-shine glow-bar"
                    style="width: 0%;"></div>
            </div>
            <p id="loadingMessage" class="text-center mt-4 text-sm text-gray-300 h-5 font-medium tracking-wide">
                Initializing...
            </p>
            <div id="waitTimerContainer" class="text-center mt-2 text-gray-300 h-10"></div>
        </div>
        <div class="absolute bottom-16 w-full text-center text-lg text-yellow-300 font-semibold animate-pulse">
            <p>Warning: ⚠️ Don't close tab — amount may be deducted.</p>
        </div>
        <div class="absolute bottom-6 text-center text-sm text-gray-400">
            powered by <span class="font-semibold text-white">GamyDay-Amrita</span>
        </div>
    </div>

    <!-- Game UI -->
    <div id="gameUI">
        <main class="main-container">
            <h1 class="game-title">Chess</h1>

            <!-- Game -->
            <div id="game" class="game-mode">
                <div class="game-container">
                    <div class="game-board-container">
                        <div id="game-board"></div>
                    </div>

                    <div class="game-info">
                        <!-- Timer Section -->
                        <div class="timer-container">
                            <div class="timer-section">
                                <div class="timer-label">White</div>
                                <div id="white-timer" class="timer">10:00</div>
                            </div>
                            <div class="timer-section">
                                <div class="timer-label">Black</div>
                                <div id="black-timer" class="timer">10:00</div>
                            </div>
                        </div>

                        <div class="status-container">
                            <div id="status-message" class="status-message">Game ready. White to move.</div>
                            <div id="player-info" class="player-info">
                                <span class="player-info-label">You are playing as:</span>
                                <span id="player-color" class="player-color">White</span>
                            </div>
                            <div class="turn-indicator my-turn">Your Turn</div>
                        </div>

                        <div class="captured-pieces-container">
                            <div class="captured-title">White Captured:</div>
                            <div id="white-captured" class="captured-pieces"></div>
                            <div class="captured-title">Black Captured:</div>
                            <div id="black-captured" class="captured-pieces"></div>
                        </div>

                        <div class="move-list-container">
                            <div class="move-list-title">Move History:</div>
                            <div id="move-list" class="move-list"></div>
                        </div>

                        <div class="footer-credit">
                            <p>Built with ❤️ by <a href="https://www.instagram.com/jangra_gitesh/?igsh=b2hlcW4yeHN1dnhl#" target="_blank">Jangra Gitesh</a></p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- GameDay Modal -->
    <div id="modal">
        <div class="modal-content">
            <h2 id="modalTitle" class="font-fredoka text-4xl font-bold text-white mb-4"></h2>
            <p id="modalMessage" class="text-xl text-gray-300 mb-8"></p>
            <div class="mt-4 text-lg text-yellow-300 font-semibold">
                <p>Warning: ⚠️ Don't refresh tab — amount may be deducted.</p>
            </div>
        </div>
    </div>

    <!-- Game Over Popup (Legacy - keeping for compatibility) -->
    <div id="game-over-popup" class="popup-overlay">
        <div class="popup-content">
            <div class="popup-body">
                <div class="result-icon">
                    <i id="result-icon" class="fas fa-crown"></i>
                </div>
                <h2 id="game-result-title">Game Over</h2>
                <p id="game-result-message">Checkmate! White wins!</p>
                <div class="game-stats">
                    <div class="stat-item">
                        <span class="stat-label">Total Moves:</span>
                        <span id="total-moves">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Game Duration:</span>
                        <span id="game-duration">00:00</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Result:</span>
                        <span id="final-result">Checkmate</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Promotion Popup -->
    <div id="promotion-popup" class="popup-overlay">
        <div class="popup-content promotion-content">
            <div class="popup-body">
                <h2>Choose Promotion</h2>
                <p>Select a piece to promote your pawn:</p>
                <div class="promotion-options">
                    <div class="promotion-piece" data-piece="q">
                        <div class="piece-icon"></div>
                        <span>Queen</span>
                    </div>
                    <div class="promotion-piece" data-piece="r">
                        <div class="piece-icon"></div>
                        <span>Rook</span>
                    </div>
                    <div class="promotion-piece" data-piece="b">
                        <div class="piece-icon"></div>
                        <span>Bishop</span>
                    </div>
                    <div class="promotion-piece" data-piece="n">
                        <div class="piece-icon"></div>
                        <span>Knight</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Scripts -->
    <script src="chess.js"></script>
</body>
</html>

